#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Test de Vercel MCP server
function testVercelMCPServer() {
  console.log('🚀 Starting Vercel MCP Server test...\n');
  
  const serverPath = join(__dirname, 'dist', 'vercel-server.js');
  const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let responseCount = 0;
  const expectedResponses = 4;

  server.stdout.on('data', (data) => {
    const response = data.toString();
    console.log('📥 Server response:', response);
    responseCount++;
    
    if (responseCount >= expectedResponses) {
      console.log('\n✅ All Vercel tests completed successfully!');
      server.kill();
      process.exit(0);
    }
  });

  server.stderr.on('data', (data) => {
    console.log('📝 Server log:', data.toString());
  });

  server.on('close', (code) => {
    console.log(`\n🔚 Vercel server process exited with code ${code}`);
  });

  // Test 1: List tools
  setTimeout(() => {
    console.log('📤 Test 1: Listing Vercel tools...');
    const listToolsRequest = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/list',
      params: {}
    };
    server.stdin.write(JSON.stringify(listToolsRequest) + '\n');
  }, 100);

  // Test 2: Get team info
  setTimeout(() => {
    console.log('📤 Test 2: Getting team info...');
    const teamInfoRequest = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'get_team_info',
        arguments: {}
      }
    };
    server.stdin.write(JSON.stringify(teamInfoRequest) + '\n');
  }, 200);

  // Test 3: List projects
  setTimeout(() => {
    console.log('📤 Test 3: Listing projects...');
    const projectsRequest = {
      jsonrpc: '2.0',
      id: 3,
      method: 'tools/call',
      params: {
        name: 'list_projects',
        arguments: {
          limit: 5
        }
      }
    };
    server.stdin.write(JSON.stringify(projectsRequest) + '\n');
  }, 300);

  // Test 4: AI Gateway stats
  setTimeout(() => {
    console.log('📤 Test 4: Getting AI Gateway stats...');
    const aiGatewayRequest = {
      jsonrpc: '2.0',
      id: 4,
      method: 'tools/call',
      params: {
        name: 'ai_gateway_stats',
        arguments: {
          timeframe: '24h'
        }
      }
    };
    server.stdin.write(JSON.stringify(aiGatewayRequest) + '\n');
  }, 400);

  // Timeout fallback
  setTimeout(() => {
    console.log('\n⏰ Test timeout reached');
    server.kill();
    process.exit(1);
  }, 10000);
}

testVercelMCPServer();
