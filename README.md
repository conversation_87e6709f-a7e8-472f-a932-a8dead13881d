# MCP Servers Collection

Een verzameling van Model Context Protocol (MCP) server implementaties die AI assistenten zoals <PERSON> in staat stelt om met externe systemen te communiceren.

## Wat is MCP?

Het Model Context Protocol (MCP) is een open standaard die AI assistenten verbindt met externe data bronnen en tools. Het stelt AI modellen in staat om:

- Toegang te krijgen tot lokale bestanden en databases
- Externe API's aan te roepen
- Complexe workflows uit te voeren
- Real-time data op te halen

## Beschikbare Servers

### 1. Basic MCP Server (`src/index.ts`)

Basis MCP server met algemene tools:

- **echo**: Echo tekst terug
- **calculate**: Voer basis wiskundige berekeningen uit
- **get_time**: Krijg de huidige datum en tijd
- **generate_uuid**: <PERSON>reer een random UUID
- **base64_encode**: Encodeer tekst naar base64
- **base64_decode**: Decodeer base64 naar tekst

### 2. Vercel MCP Server (`src/vercel-server.ts`)

Gespecialiseerde server voor Vercel API integratie:

- **list_projects**: Lijst alle Vercel projecten
- **get_project**: Haal details van een specifiek project op
- **list_deployments**: Lijst deployments voor een project
- **get_deployment**: Haal details van een specifieke deployment op
- **list_domains**: Lijst alle domeinen
- **get_team_info**: Haal team informatie op
- **ai_gateway_stats**: Haal AI Gateway statistieken op
- **ai_gateway_logs**: Haal AI Gateway logs op

### 3. LM Studio MCP Server (`src/lmstudio-server.ts`)

Lokale AI model integratie via LM Studio:

- **list_models**: Lijst alle beschikbare modellen in LM Studio
- **chat_completion**: Stuur berichten naar LM Studio modellen
- **get_server_info**: Haal LM Studio server informatie op
- **check_connection**: Controleer verbinding met LM Studio
- **embeddings**: Genereer text embeddings

## Installatie

1. Installeer dependencies:
```bash
npm install
```

2. Build het project:
```bash
npm run build
```

3. Start een server:

**Basic MCP Server:**
```bash
npm start
# of voor development:
npm run dev
```

**Vercel MCP Server:**
```bash
npm run start:vercel
# of voor development:
npm run dev:vercel
```

**LM Studio MCP Server:**
```bash
npm run start:lmstudio
# of voor development:
npm run dev:lmstudio
```

## Environment Configuratie

Kopieer `.env.example` naar `.env` en vul je eigen API keys in:

```bash
cp .env.example .env
```

Bewerk `.env`:
```env
VERCEL_TOKEN=your_vercel_token_here
VERCEL_TEAM_ID=your_team_id_here
AI_GATEWAY_API_KEY=your_ai_gateway_key_here
```

## Claude Desktop Configuratie

Om deze MCP servers te gebruiken met Claude Desktop, voeg het volgende toe aan je Claude Desktop configuratie:

### macOS
Bestand: `~/Library/Application Support/Claude/claude_desktop_config.json`

### Windows
Bestand: `%APPDATA%/Claude/claude_desktop_config.json`

Kopieer de inhoud van `claude-desktop-config.json` of gebruik deze configuratie:

```json
{
  "mcpServers": {
    "basic-mcp-server": {
      "command": "node",
      "args": ["/Users/<USER>/.continue/mcp/dist/index.js"]
    },
    "vercel-mcp-server": {
      "command": "node",
      "args": ["/Users/<USER>/.continue/mcp/dist/vercel-server.js"],
      "env": {
        "VERCEL_TOKEN": "your_vercel_token",
        "VERCEL_TEAM_ID": "your_team_id",
        "AI_GATEWAY_API_KEY": "your_ai_gateway_key"
      }
    }
  }
}
```

**Belangrijk:** Vervang de paden en API keys met je eigen waarden!

## Project Structuur

```
mcp-server/
├── src/
│   └── index.ts          # Hoofd server implementatie
├── dist/                 # Gecompileerde JavaScript
├── package.json          # Project configuratie
├── tsconfig.json         # TypeScript configuratie
└── README.md            # Deze file
```

## Uitbreiden

Om nieuwe tools toe te voegen:

1. Voeg de tool definitie toe aan de `ListToolsRequestSchema` handler
2. Implementeer de tool logica in de `CallToolRequestSchema` handler
3. Test je nieuwe tool

Voorbeeld van een nieuwe tool:

```typescript
{
  name: 'mijn_tool',
  description: 'Beschrijving van wat de tool doet',
  inputSchema: {
    type: 'object',
    properties: {
      parameter: {
        type: 'string',
        description: 'Parameter beschrijving',
      },
    },
    required: ['parameter'],
  },
}
```

## Testing

Run tests met:
```bash
npm test
```

## Linting en Formatting

```bash
npm run lint
npm run format
```

## Troubleshooting

### Server start niet
- Controleer of alle dependencies geïnstalleerd zijn
- Zorg ervoor dat het project gebuild is (`npm run build`)
- Controleer de console voor error berichten

### Claude Desktop herkent de server niet
- Controleer het pad in de configuratie file
- Herstart Claude Desktop na configuratie wijzigingen
- Controleer of de server executable is

## Licentie

MIT
