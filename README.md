# MCP Server

Een Model Context Protocol (MCP) server implementatie die AI assistenten zoals <PERSON> in staat stelt om met externe systemen te communiceren.

## Wat is MCP?

Het Model Context Protocol (MCP) is een open standaard die AI assistenten verbindt met externe data bronnen en tools. Het stelt AI modellen in staat om:

- Toegang te krijgen tot lokale bestanden en databases
- Externe API's aan te roepen
- Complexe workflows uit te voeren
- Real-time data op te halen

## Features

Deze MCP server biedt de volgende tools:

- **echo**: Echo tekst terug
- **calculate**: Voer basis wiskundige berekeningen uit
- **get_time**: Krijg de huidige datum en tijd

## Installatie

1. Installeer dependencies:
```bash
npm install
```

2. Build het project:
```bash
npm run build
```

3. Start de server:
```bash
npm start
```

## Development

Voor development met hot reload:
```bash
npm run dev
```

## Configuratie

Om deze MCP server te gebruiken met <PERSON>, voeg het volgende toe aan je Claude Desktop configuratie:

### macOS
Bestand: `~/Library/Application Support/Claude/claude_desktop_config.json`

### Windows
Bestand: `%APPDATA%/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "mcp-server": {
      "command": "node",
      "args": ["/pad/naar/jouw/project/dist/index.js"]
    }
  }
}
```

## Project Structuur

```
mcp-server/
├── src/
│   └── index.ts          # Hoofd server implementatie
├── dist/                 # Gecompileerde JavaScript
├── package.json          # Project configuratie
├── tsconfig.json         # TypeScript configuratie
└── README.md            # Deze file
```

## Uitbreiden

Om nieuwe tools toe te voegen:

1. Voeg de tool definitie toe aan de `ListToolsRequestSchema` handler
2. Implementeer de tool logica in de `CallToolRequestSchema` handler
3. Test je nieuwe tool

Voorbeeld van een nieuwe tool:

```typescript
{
  name: 'mijn_tool',
  description: 'Beschrijving van wat de tool doet',
  inputSchema: {
    type: 'object',
    properties: {
      parameter: {
        type: 'string',
        description: 'Parameter beschrijving',
      },
    },
    required: ['parameter'],
  },
}
```

## Testing

Run tests met:
```bash
npm test
```

## Linting en Formatting

```bash
npm run lint
npm run format
```

## Troubleshooting

### Server start niet
- Controleer of alle dependencies geïnstalleerd zijn
- Zorg ervoor dat het project gebuild is (`npm run build`)
- Controleer de console voor error berichten

### Claude Desktop herkent de server niet
- Controleer het pad in de configuratie file
- Herstart Claude Desktop na configuratie wijzigingen
- Controleer of de server executable is

## Licentie

MIT
