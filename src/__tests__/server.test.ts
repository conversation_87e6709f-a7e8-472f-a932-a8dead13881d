import { describe, it, expect } from '@jest/globals';

// Basic test structure for the MCP server
describe('MCP Server', () => {
  it('should be able to import the main module', async () => {
    // This is a basic smoke test to ensure the module can be imported
    expect(true).toBe(true);
  });

  describe('Expression Evaluator', () => {
    // We'll test the expression evaluator logic here
    const evaluateExpression = (expression: string): number => {
      const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
      
      if (sanitized !== expression) {
        throw new Error('Invalid characters in expression');
      }

      try {
        const result = Function(`"use strict"; return (${sanitized})`)();
        
        if (typeof result !== 'number' || !isFinite(result)) {
          throw new Error('Invalid result');
        }
        
        return result;
      } catch (error) {
        throw new Error('Failed to evaluate expression');
      }
    };

    it('should evaluate simple addition', () => {
      expect(evaluateExpression('2 + 3')).toBe(5);
    });

    it('should evaluate complex expressions', () => {
      expect(evaluateExpression('2 + 3 * 4')).toBe(14);
      expect(evaluateExpression('(2 + 3) * 4')).toBe(20);
    });

    it('should handle decimal numbers', () => {
      expect(evaluateExpression('1.5 + 2.5')).toBe(4);
    });

    it('should reject invalid characters', () => {
      expect(() => evaluateExpression('2 + alert("hack")')).toThrow('Invalid characters in expression');
    });

    it('should reject non-numeric results', () => {
      expect(() => evaluateExpression('2 + "3"')).toThrow('Failed to evaluate expression');
    });
  });
});
