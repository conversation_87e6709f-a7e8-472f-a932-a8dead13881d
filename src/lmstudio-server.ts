#!/usr/bin/env node

import { config } from 'dotenv';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';

// Load environment variables
config();

/**
 * LM Studio MCP Server
 * 
 * Deze server biedt tools voor het communiceren met LM Studio's lokale API.
 * LM Studio draait standaard op http://localhost:1234 met OpenAI-compatible API.
 */

interface LMStudioConfig {
  baseUrl: string;
  apiKey?: string;
}

class LMStudioMCPServer {
  private server: Server;
  private config: LMStudioConfig;

  constructor() {
    // LM Studio configuratie
    this.config = {
      baseUrl: process.env.LM_STUDIO_BASE_URL || 'http://localhost:1234',
      apiKey: process.env.LM_STUDIO_API_KEY || '', // LM Studio vereist meestal geen API key
    };

    this.server = new Server({
      name: 'lmstudio-mcp-server',
      version: '1.0.0',
    });

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  private async makeLMStudioRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${this.config.baseUrl}${endpoint}`;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...options.headers as Record<string, string>,
    };

    if (this.config.apiKey) {
      headers['Authorization'] = `Bearer ${this.config.apiKey}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`LM Studio API error: ${response.status} - ${errorText}`);
      }

      return response.json();
    } catch (error) {
      if (error instanceof Error && error.message.includes('fetch')) {
        throw new Error('Kan geen verbinding maken met LM Studio. Zorg dat LM Studio draait op ' + this.config.baseUrl);
      }
      throw error;
    }
  }

  private setupToolHandlers(): void {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'list_models',
            description: 'Lijst alle beschikbare modellen in LM Studio',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
          {
            name: 'chat_completion',
            description: 'Stuur een chat bericht naar LM Studio',
            inputSchema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  description: 'Het bericht om te sturen',
                },
                model: {
                  type: 'string',
                  description: 'Model naam (optioneel, gebruikt het geladen model)',
                },
                temperature: {
                  type: 'number',
                  description: 'Temperature voor response (0.0-2.0)',
                  default: 0.7,
                },
                max_tokens: {
                  type: 'number',
                  description: 'Maximum aantal tokens in response',
                  default: 1000,
                },
              },
              required: ['message'],
            },
          },
          {
            name: 'get_server_info',
            description: 'Haal LM Studio server informatie op',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
          {
            name: 'check_connection',
            description: 'Controleer verbinding met LM Studio',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
          {
            name: 'embeddings',
            description: 'Genereer embeddings voor tekst',
            inputSchema: {
              type: 'object',
              properties: {
                input: {
                  type: 'string',
                  description: 'Tekst om embeddings voor te genereren',
                },
                model: {
                  type: 'string',
                  description: 'Embedding model (optioneel)',
                },
              },
              required: ['input'],
            },
          },
        ] as Tool[],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'list_models':
            return await this.listModels();
          
          case 'chat_completion':
            return await this.chatCompletion(args);
          
          case 'get_server_info':
            return await this.getServerInfo();
          
          case 'check_connection':
            return await this.checkConnection();
          
          case 'embeddings':
            return await this.generateEmbeddings(args);

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  private async listModels() {
    const data = await this.makeLMStudioRequest('/v1/models');
    
    if (!data.data || data.data.length === 0) {
      return {
        content: [
          {
            type: 'text',
            text: '📋 Geen modellen gevonden.\n\n💡 Tip: Laad een model in LM Studio om het hier te zien.',
          },
        ],
      };
    }

    const modelList = data.data.map((model: any) => 
      `• ${model.id}\n  Created: ${new Date(model.created * 1000).toLocaleDateString()}\n  Owner: ${model.owned_by}`
    ).join('\n\n');

    return {
      content: [
        {
          type: 'text',
          text: `🤖 Beschikbare Modellen (${data.data.length}):\n\n${modelList}`,
        },
      ],
    };
  }

  private async chatCompletion(args: any) {
    const message = (args as any)?.message;
    const model = (args as any)?.model;
    const temperature = (args as any)?.temperature || 0.7;
    const maxTokens = (args as any)?.max_tokens || 1000;

    if (!message) {
      throw new Error('Message is required');
    }

    const requestBody = {
      model: model || undefined, // LM Studio gebruikt het geladen model als geen model gespecificeerd
      messages: [
        {
          role: 'user',
          content: message,
        },
      ],
      temperature: Math.max(0, Math.min(2, temperature)),
      max_tokens: Math.max(1, Math.min(4000, maxTokens)),
    };

    const data = await this.makeLMStudioRequest('/v1/chat/completions', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = data.choices?.[0]?.message?.content || 'Geen response ontvangen';
    const usage = data.usage;

    return {
      content: [
        {
          type: 'text',
          text: `🤖 LM Studio Response:\n\n${response}\n\n` +
                `📊 Usage:\n` +
                `• Prompt tokens: ${usage?.prompt_tokens || 'N/A'}\n` +
                `• Completion tokens: ${usage?.completion_tokens || 'N/A'}\n` +
                `• Total tokens: ${usage?.total_tokens || 'N/A'}`,
        },
      ],
    };
  }

  private async getServerInfo() {
    try {
      // Probeer modellen op te halen om server info te krijgen
      const modelsData = await this.makeLMStudioRequest('/v1/models');
      
      return {
        content: [
          {
            type: 'text',
            text: `🖥️ LM Studio Server Info:\n\n` +
                  `🌐 Base URL: ${this.config.baseUrl}\n` +
                  `🔑 API Key: ${this.config.apiKey ? 'Configured' : 'Not configured'}\n` +
                  `🤖 Loaded Models: ${modelsData.data?.length || 0}\n` +
                  `✅ Status: Connected`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `🖥️ LM Studio Server Info:\n\n` +
                  `🌐 Base URL: ${this.config.baseUrl}\n` +
                  `🔑 API Key: ${this.config.apiKey ? 'Configured' : 'Not configured'}\n` +
                  `❌ Status: ${error instanceof Error ? error.message : 'Connection failed'}`,
          },
        ],
      };
    }
  }

  private async checkConnection() {
    try {
      await this.makeLMStudioRequest('/v1/models');
      
      return {
        content: [
          {
            type: 'text',
            text: `✅ Verbinding met LM Studio succesvol!\n\n` +
                  `🌐 Connected to: ${this.config.baseUrl}\n` +
                  `📡 API is responsive`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `❌ Kan geen verbinding maken met LM Studio:\n\n` +
                  `🌐 Trying: ${this.config.baseUrl}\n` +
                  `💡 Zorg dat LM Studio draait en de server is gestart\n` +
                  `🔧 Error: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
      };
    }
  }

  private async generateEmbeddings(args: any) {
    const input = (args as any)?.input;
    const model = (args as any)?.model;

    if (!input) {
      throw new Error('Input text is required');
    }

    const requestBody = {
      model: model || 'text-embedding-ada-002', // Default embedding model
      input: input,
    };

    try {
      const data = await this.makeLMStudioRequest('/v1/embeddings', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });

      const embedding = data.data?.[0]?.embedding;
      const usage = data.usage;

      return {
        content: [
          {
            type: 'text',
            text: `🔢 Embeddings Generated:\n\n` +
                  `📝 Input: "${input.substring(0, 100)}${input.length > 100 ? '...' : ''}"\n` +
                  `📊 Dimensions: ${embedding?.length || 'N/A'}\n` +
                  `🎯 First 5 values: [${embedding?.slice(0, 5).map((v: number) => v.toFixed(4)).join(', ') || 'N/A'}]\n` +
                  `📈 Usage: ${usage?.total_tokens || 'N/A'} tokens`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `❌ Embeddings not supported or model not loaded:\n\n` +
                  `💡 Tip: Zorg dat een embedding-compatible model geladen is in LM Studio\n` +
                  `🔧 Error: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
      };
    }
  }

  private setupErrorHandling(): void {
    this.server.onerror = (error) => {
      console.error('[LM Studio MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  async run(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('LM Studio MCP Server running on stdio');
  }
}

// Start the server
const server = new LMStudioMCPServer();
server.run().catch((error) => {
  console.error('Failed to start LM Studio MCP server:', error);
  process.exit(1);
});
