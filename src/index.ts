#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';

/**
 * MCP Server Implementation
 * 
 * This server provides tools for basic operations like file system access,
 * calculations, and text processing.
 */

class MCPServer {
  private server: Server;

  constructor() {
    this.server = new Server(
      {
        name: 'mcp-server',
        version: '1.0.0',
      }
    );

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  private setupToolHandlers(): void {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'echo',
            description: 'Echo back the input text',
            inputSchema: {
              type: 'object',
              properties: {
                text: {
                  type: 'string',
                  description: 'Text to echo back',
                },
              },
              required: ['text'],
            },
          },
          {
            name: 'calculate',
            description: 'Perform basic mathematical calculations',
            inputSchema: {
              type: 'object',
              properties: {
                expression: {
                  type: 'string',
                  description: 'Mathematical expression to evaluate (e.g., "2 + 3 * 4")',
                },
              },
              required: ['expression'],
            },
          },
          {
            name: 'get_time',
            description: 'Get the current date and time',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
          {
            name: 'generate_uuid',
            description: 'Generate a random UUID',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
          {
            name: 'base64_encode',
            description: 'Encode text to base64',
            inputSchema: {
              type: 'object',
              properties: {
                text: {
                  type: 'string',
                  description: 'Text to encode',
                },
              },
              required: ['text'],
            },
          },
          {
            name: 'base64_decode',
            description: 'Decode base64 to text',
            inputSchema: {
              type: 'object',
              properties: {
                encoded: {
                  type: 'string',
                  description: 'Base64 encoded text to decode',
                },
              },
              required: ['encoded'],
            },
          },
        ] as Tool[],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'echo':
            return {
              content: [
                {
                  type: 'text',
                  text: `Echo: ${(args as any)?.text || 'No text provided'}`,
                },
              ],
            };

          case 'calculate':
            const expression = (args as any)?.expression;
            if (typeof expression !== 'string') {
              throw new Error('Expression must be a string');
            }
            const result = this.evaluateExpression(expression);
            return {
              content: [
                {
                  type: 'text',
                  text: `Result: ${result}`,
                },
              ],
            };

          case 'get_time':
            const now = new Date();
            return {
              content: [
                {
                  type: 'text',
                  text: `Current time: ${now.toISOString()}`,
                },
              ],
            };

          case 'generate_uuid':
            const uuid = this.generateUUID();
            return {
              content: [
                {
                  type: 'text',
                  text: `Generated UUID: ${uuid}`,
                },
              ],
            };

          case 'base64_encode':
            const textToEncode = (args as any)?.text;
            if (typeof textToEncode !== 'string') {
              throw new Error('Text must be a string');
            }
            const encoded = Buffer.from(textToEncode, 'utf8').toString('base64');
            return {
              content: [
                {
                  type: 'text',
                  text: `Encoded: ${encoded}`,
                },
              ],
            };

          case 'base64_decode':
            const encodedText = (args as any)?.encoded;
            if (typeof encodedText !== 'string') {
              throw new Error('Encoded text must be a string');
            }
            try {
              const decoded = Buffer.from(encodedText, 'base64').toString('utf8');
              return {
                content: [
                  {
                    type: 'text',
                    text: `Decoded: ${decoded}`,
                  },
                ],
              };
            } catch (error) {
              throw new Error('Invalid base64 encoding');
            }

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  private evaluateExpression(expression: string): number {
    // Simple and safe expression evaluator
    // Only allows basic arithmetic operations
    const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');

    if (sanitized !== expression) {
      throw new Error('Invalid characters in expression');
    }

    try {
      // Use Function constructor for safe evaluation
      const result = Function(`"use strict"; return (${sanitized})`)();

      if (typeof result !== 'number' || !isFinite(result)) {
        throw new Error('Invalid result');
      }

      return result;
    } catch (error) {
      throw new Error('Failed to evaluate expression');
    }
  }

  private generateUUID(): string {
    // Simple UUID v4 generator
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  private setupErrorHandling(): void {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  async run(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('MCP Server running on stdio');
  }
}

// Start the server
const server = new MCPServer();
server.run().catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
