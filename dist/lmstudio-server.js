#!/usr/bin/env node
import { config } from 'dotenv';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema, } from '@modelcontextprotocol/sdk/types.js';
// Load environment variables
config();
class LMStudioMCPServer {
    server;
    config;
    constructor() {
        // LM Studio configuratie
        this.config = {
            baseUrl: process.env.LM_STUDIO_BASE_URL || 'http://localhost:1234',
            apiKey: process.env.LM_STUDIO_API_KEY || '', // LM Studio vereist meestal geen API key
        };
        this.server = new Server({
            name: 'lmstudio-mcp-server',
            version: '1.0.0',
        });
        this.setupToolHandlers();
        this.setupErrorHandling();
    }
    async makeLMStudioRequest(endpoint, options = {}) {
        const url = `${this.config.baseUrl}${endpoint}`;
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers,
        };
        if (this.config.apiKey) {
            headers['Authorization'] = `Bearer ${this.config.apiKey}`;
        }
        try {
            const response = await fetch(url, {
                ...options,
                headers,
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`LM Studio API error: ${response.status} - ${errorText}`);
            }
            return response.json();
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('fetch')) {
                throw new Error('Kan geen verbinding maken met LM Studio. Zorg dat LM Studio draait op ' + this.config.baseUrl);
            }
            throw error;
        }
    }
    setupToolHandlers() {
        // List available tools
        this.server.setRequestHandler(ListToolsRequestSchema, async () => {
            return {
                tools: [
                    {
                        name: 'list_models',
                        description: 'Lijst alle beschikbare modellen in LM Studio',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                        },
                    },
                    {
                        name: 'chat_completion',
                        description: 'Stuur een chat bericht naar LM Studio',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                message: {
                                    type: 'string',
                                    description: 'Het bericht om te sturen',
                                },
                                model: {
                                    type: 'string',
                                    description: 'Model naam (optioneel, gebruikt het geladen model)',
                                },
                                temperature: {
                                    type: 'number',
                                    description: 'Temperature voor response (0.0-2.0)',
                                    default: 0.7,
                                },
                                max_tokens: {
                                    type: 'number',
                                    description: 'Maximum aantal tokens in response',
                                    default: 1000,
                                },
                            },
                            required: ['message'],
                        },
                    },
                    {
                        name: 'get_server_info',
                        description: 'Haal LM Studio server informatie op',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                        },
                    },
                    {
                        name: 'check_connection',
                        description: 'Controleer verbinding met LM Studio',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                        },
                    },
                    {
                        name: 'embeddings',
                        description: 'Genereer embeddings voor tekst',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                input: {
                                    type: 'string',
                                    description: 'Tekst om embeddings voor te genereren',
                                },
                                model: {
                                    type: 'string',
                                    description: 'Embedding model (optioneel)',
                                },
                            },
                            required: ['input'],
                        },
                    },
                ],
            };
        });
        // Handle tool calls
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            try {
                switch (name) {
                    case 'list_models':
                        return await this.listModels();
                    case 'chat_completion':
                        return await this.chatCompletion(args);
                    case 'get_server_info':
                        return await this.getServerInfo();
                    case 'check_connection':
                        return await this.checkConnection();
                    case 'embeddings':
                        return await this.generateEmbeddings(args);
                    default:
                        throw new Error(`Unknown tool: ${name}`);
                }
            }
            catch (error) {
                return {
                    content: [
                        {
                            type: 'text',
                            text: `Error: ${error instanceof Error ? error.message : String(error)}`,
                        },
                    ],
                    isError: true,
                };
            }
        });
    }
    async listModels() {
        const data = await this.makeLMStudioRequest('/v1/models');
        if (!data.data || data.data.length === 0) {
            return {
                content: [
                    {
                        type: 'text',
                        text: '📋 Geen modellen gevonden.\n\n💡 Tip: Laad een model in LM Studio om het hier te zien.',
                    },
                ],
            };
        }
        const modelList = data.data.map((model) => `• ${model.id}\n  Created: ${new Date(model.created * 1000).toLocaleDateString()}\n  Owner: ${model.owned_by}`).join('\n\n');
        return {
            content: [
                {
                    type: 'text',
                    text: `🤖 Beschikbare Modellen (${data.data.length}):\n\n${modelList}`,
                },
            ],
        };
    }
    async chatCompletion(args) {
        const message = args?.message;
        const model = args?.model;
        const temperature = args?.temperature || 0.7;
        const maxTokens = args?.max_tokens || 1000;
        if (!message) {
            throw new Error('Message is required');
        }
        const requestBody = {
            model: model || undefined, // LM Studio gebruikt het geladen model als geen model gespecificeerd
            messages: [
                {
                    role: 'user',
                    content: message,
                },
            ],
            temperature: Math.max(0, Math.min(2, temperature)),
            max_tokens: Math.max(1, Math.min(4000, maxTokens)),
        };
        const data = await this.makeLMStudioRequest('/v1/chat/completions', {
            method: 'POST',
            body: JSON.stringify(requestBody),
        });
        const response = data.choices?.[0]?.message?.content || 'Geen response ontvangen';
        const usage = data.usage;
        return {
            content: [
                {
                    type: 'text',
                    text: `🤖 LM Studio Response:\n\n${response}\n\n` +
                        `📊 Usage:\n` +
                        `• Prompt tokens: ${usage?.prompt_tokens || 'N/A'}\n` +
                        `• Completion tokens: ${usage?.completion_tokens || 'N/A'}\n` +
                        `• Total tokens: ${usage?.total_tokens || 'N/A'}`,
                },
            ],
        };
    }
    async getServerInfo() {
        try {
            // Probeer modellen op te halen om server info te krijgen
            const modelsData = await this.makeLMStudioRequest('/v1/models');
            return {
                content: [
                    {
                        type: 'text',
                        text: `🖥️ LM Studio Server Info:\n\n` +
                            `🌐 Base URL: ${this.config.baseUrl}\n` +
                            `🔑 API Key: ${this.config.apiKey ? 'Configured' : 'Not configured'}\n` +
                            `🤖 Loaded Models: ${modelsData.data?.length || 0}\n` +
                            `✅ Status: Connected`,
                    },
                ],
            };
        }
        catch (error) {
            return {
                content: [
                    {
                        type: 'text',
                        text: `🖥️ LM Studio Server Info:\n\n` +
                            `🌐 Base URL: ${this.config.baseUrl}\n` +
                            `🔑 API Key: ${this.config.apiKey ? 'Configured' : 'Not configured'}\n` +
                            `❌ Status: ${error instanceof Error ? error.message : 'Connection failed'}`,
                    },
                ],
            };
        }
    }
    async checkConnection() {
        try {
            await this.makeLMStudioRequest('/v1/models');
            return {
                content: [
                    {
                        type: 'text',
                        text: `✅ Verbinding met LM Studio succesvol!\n\n` +
                            `🌐 Connected to: ${this.config.baseUrl}\n` +
                            `📡 API is responsive`,
                    },
                ],
            };
        }
        catch (error) {
            return {
                content: [
                    {
                        type: 'text',
                        text: `❌ Kan geen verbinding maken met LM Studio:\n\n` +
                            `🌐 Trying: ${this.config.baseUrl}\n` +
                            `💡 Zorg dat LM Studio draait en de server is gestart\n` +
                            `🔧 Error: ${error instanceof Error ? error.message : String(error)}`,
                    },
                ],
            };
        }
    }
    async generateEmbeddings(args) {
        const input = args?.input;
        const model = args?.model;
        if (!input) {
            throw new Error('Input text is required');
        }
        const requestBody = {
            model: model || 'text-embedding-ada-002', // Default embedding model
            input: input,
        };
        try {
            const data = await this.makeLMStudioRequest('/v1/embeddings', {
                method: 'POST',
                body: JSON.stringify(requestBody),
            });
            const embedding = data.data?.[0]?.embedding;
            const usage = data.usage;
            return {
                content: [
                    {
                        type: 'text',
                        text: `🔢 Embeddings Generated:\n\n` +
                            `📝 Input: "${input.substring(0, 100)}${input.length > 100 ? '...' : ''}"\n` +
                            `📊 Dimensions: ${embedding?.length || 'N/A'}\n` +
                            `🎯 First 5 values: [${embedding?.slice(0, 5).map((v) => v.toFixed(4)).join(', ') || 'N/A'}]\n` +
                            `📈 Usage: ${usage?.total_tokens || 'N/A'} tokens`,
                    },
                ],
            };
        }
        catch (error) {
            return {
                content: [
                    {
                        type: 'text',
                        text: `❌ Embeddings not supported or model not loaded:\n\n` +
                            `💡 Tip: Zorg dat een embedding-compatible model geladen is in LM Studio\n` +
                            `🔧 Error: ${error instanceof Error ? error.message : String(error)}`,
                    },
                ],
            };
        }
    }
    setupErrorHandling() {
        this.server.onerror = (error) => {
            console.error('[LM Studio MCP Error]', error);
        };
        process.on('SIGINT', async () => {
            await this.server.close();
            process.exit(0);
        });
    }
    async run() {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        console.error('LM Studio MCP Server running on stdio');
    }
}
// Start the server
const server = new LMStudioMCPServer();
server.run().catch((error) => {
    console.error('Failed to start LM Studio MCP server:', error);
    process.exit(1);
});
//# sourceMappingURL=lmstudio-server.js.map