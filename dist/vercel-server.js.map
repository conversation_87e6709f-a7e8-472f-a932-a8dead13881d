{"version": 3, "file": "vercel-server.js", "sourceRoot": "", "sources": ["../src/vercel-server.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,2CAA2C,CAAC;AACnE,OAAO,EAAE,oBAAoB,EAAE,MAAM,2CAA2C,CAAC;AACjF,OAAO,EACL,qBAAqB,EACrB,sBAAsB,GAEvB,MAAM,oCAAoC,CAAC;AAE5C,6BAA6B;AAC7B,MAAM,EAAE,CAAC;AAeT,MAAM,eAAe;IACX,MAAM,CAAS;IACf,MAAM,CAAe;IACrB,OAAO,GAAG,wBAAwB,CAAC;IAE3C;QACE,yCAAyC;QACzC,IAAI,CAAC,MAAM,GAAG;YACZ,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,0BAA0B;YAC7D,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,0BAA0B;YAChE,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,0BAA0B;SAC3E,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;YACvB,IAAI,EAAE,mBAAmB;YACzB,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,UAAuB,EAAE;QACzE,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,QAAQ,EAAE,CAAC;QACzC,MAAM,OAAO,GAAG;YACd,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YAC9C,cAAc,EAAE,kBAAkB;YAClC,GAAG,OAAO,CAAC,OAAO;SACnB,CAAC;QAEF,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,SAAS,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YACrD,QAAQ,IAAI,GAAG,SAAS,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACzD,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,QAAQ,EAAE,EAAE;YACzD,GAAG,OAAO;YACV,OAAO;SACR,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,CAAC,MAAM,MAAM,SAAS,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAEO,iBAAiB;QACvB,uBAAuB;QACvB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;YAC/D,OAAO;gBACL,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,eAAe;wBACrB,WAAW,EAAE,6BAA6B;wBAC1C,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,KAAK,EAAE;oCACL,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,2CAA2C;oCACxD,OAAO,EAAE,EAAE;iCACZ;6BACF;yBACF;qBACF;oBACD;wBACE,IAAI,EAAE,aAAa;wBACnB,WAAW,EAAE,2CAA2C;wBACxD,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,SAAS,EAAE;oCACT,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,oBAAoB;iCAClC;6BACF;4BACD,QAAQ,EAAE,CAAC,WAAW,CAAC;yBACxB;qBACF;oBACD;wBACE,IAAI,EAAE,kBAAkB;wBACxB,WAAW,EAAE,oCAAoC;wBACjD,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,SAAS,EAAE;oCACT,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,wBAAwB;iCACtC;gCACD,KAAK,EAAE;oCACL,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,8BAA8B;oCAC3C,OAAO,EAAE,EAAE;iCACZ;6BACF;yBACF;qBACF;oBACD;wBACE,IAAI,EAAE,gBAAgB;wBACtB,WAAW,EAAE,+CAA+C;wBAC5D,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,YAAY,EAAE;oCACZ,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,eAAe;iCAC7B;6BACF;4BACD,QAAQ,EAAE,CAAC,cAAc,CAAC;yBAC3B;qBACF;oBACD;wBACE,IAAI,EAAE,cAAc;wBACpB,WAAW,EAAE,qBAAqB;wBAClC,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,KAAK,EAAE;oCACL,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,2BAA2B;oCACxC,OAAO,EAAE,EAAE;iCACZ;6BACF;yBACF;qBACF;oBACD;wBACE,IAAI,EAAE,eAAe;wBACrB,WAAW,EAAE,yBAAyB;wBACtC,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE,EAAE;yBACf;qBACF;oBACD;wBACE,IAAI,EAAE,kBAAkB;wBACxB,WAAW,EAAE,iCAAiC;wBAC9C,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,SAAS,EAAE;oCACT,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,iCAAiC;oCAC9C,OAAO,EAAE,KAAK;iCACf;6BACF;yBACF;qBACF;oBACD;wBACE,IAAI,EAAE,iBAAiB;wBACvB,WAAW,EAAE,yBAAyB;wBACtC,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,KAAK,EAAE;oCACL,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,uBAAuB;oCACpC,OAAO,EAAE,EAAE;iCACZ;6BACF;yBACF;qBACF;iBACQ;aACZ,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;YACrE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;YAEjD,IAAI,CAAC;gBACH,QAAQ,IAAI,EAAE,CAAC;oBACb,KAAK,eAAe;wBAClB,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBAEvC,KAAK,aAAa;wBAChB,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;oBAErC,KAAK,kBAAkB;wBACrB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oBAE1C,KAAK,gBAAgB;wBACnB,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;oBAExC,KAAK,cAAc;wBACjB,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;oBAEtC,KAAK,eAAe;wBAClB,OAAO,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;oBAElC,KAAK,kBAAkB;wBACrB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBAE5C,KAAK,iBAAiB;wBACpB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;oBAE3C;wBACE,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,EAAE,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE;wBACP;4BACE,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;yBACzE;qBACF;oBACD,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,IAAS;QAClC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAE,IAAY,EAAE,KAAK,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;QACxD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,KAAK,EAAE,CAAC,CAAC;QAEzE,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAY,EAAE,EAAE,CACrD,KAAK,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,EAAE,mBAAmB,OAAO,CAAC,SAAS,IAAI,KAAK,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,oBAAoB,OAAO,CAAC,IAAI,aAAa,CAC3L,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEf,OAAO;YACL,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,wBAAwB,IAAI,CAAC,QAAQ,CAAC,MAAM,SAAS,WAAW,EAAE;iBACzE;aACF;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,IAAS;QAChC,MAAM,SAAS,GAAI,IAAY,EAAE,SAAS,CAAC;QAC3C,IAAI,CAAC,SAAS;YAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAE1D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,SAAS,EAAE,CAAC,CAAC;QAEvE,OAAO;YACL,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,eAAe,IAAI,CAAC,IAAI,IAAI;wBAC5B,UAAU,IAAI,CAAC,EAAE,IAAI;wBACrB,iBAAiB,IAAI,CAAC,SAAS,IAAI,KAAK,IAAI;wBAC5C,eAAe,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,IAAI;wBAChE,eAAe,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,IAAI;wBAChE,8BAA8B,IAAI,CAAC,IAAI,eAAe;wBACtD,sBAAsB,IAAI,CAAC,aAAa,IAAI,GAAG,IAAI;wBACnD,qBAAqB,IAAI,CAAC,YAAY,IAAI,eAAe,IAAI;wBAC7D,wBAAwB,IAAI,CAAC,eAAe,IAAI,eAAe,EAAE;iBACxE;aACF;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,IAAS;QACrC,MAAM,SAAS,GAAI,IAAY,EAAE,SAAS,CAAC;QAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAE,IAAY,EAAE,KAAK,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;QAExD,IAAI,QAAQ,GAAG,yBAAyB,KAAK,EAAE,CAAC;QAChD,IAAI,SAAS,EAAE,CAAC;YACd,QAAQ,IAAI,cAAc,SAAS,EAAE,CAAC;QACxC,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAEpD,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAe,EAAE,EAAE,CAC9D,KAAK,UAAU,CAAC,GAAG,cAAc,UAAU,CAAC,KAAK,gBAAgB,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,WAAW,UAAU,CAAC,GAAG,EAAE,CAChJ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEf,OAAO;YACL,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,mBAAmB,IAAI,CAAC,WAAW,CAAC,MAAM,SAAS,cAAc,EAAE;iBAC1E;aACF;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,IAAS;QACnC,MAAM,YAAY,GAAI,IAAY,EAAE,YAAY,CAAC;QACjD,IAAI,CAAC,YAAY;YAAE,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAEhE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,YAAY,EAAE,CAAC,CAAC;QAE9E,OAAO;YACL,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,kBAAkB,IAAI,CAAC,GAAG,IAAI;wBAC9B,UAAU,IAAI,CAAC,GAAG,IAAI;wBACtB,aAAa,IAAI,CAAC,KAAK,IAAI;wBAC3B,eAAe,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,IAAI;wBAChE,kBAAkB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI;wBAC/J,mBAAmB,IAAI,CAAC,GAAG,IAAI;wBAC/B,eAAe,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI;wBACrC,cAAc,IAAI,CAAC,IAAI,EAAE,mBAAmB,IAAI,KAAK,EAAE;iBAC9D;aACF;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,IAAS;QACjC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAE,IAAY,EAAE,KAAK,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;QACxD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,KAAK,EAAE,CAAC,CAAC;QAExE,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAClD,KAAK,MAAM,CAAC,IAAI,iBAAiB,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,EAAE,CAC9H,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEf,OAAO;YACL,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,gBAAgB,IAAI,CAAC,OAAO,CAAC,MAAM,SAAS,UAAU,EAAE;iBAC/D;aACF;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAEtD,OAAO;YACL,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,oBAAoB;wBACpB,aAAa,IAAI,CAAC,KAAK,IAAI;wBAC3B,gBAAgB,IAAI,CAAC,QAAQ,IAAI;wBACjC,UAAU,IAAI,CAAC,EAAE,IAAI;wBACrB,eAAe,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,EAAE;iBACrE;aACF;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,IAAS;QACvC,MAAM,SAAS,GAAI,IAAY,EAAE,SAAS,IAAI,KAAK,CAAC;QAEpD,IAAI,CAAC;YACH,yEAAyE;YACzE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,kCAAkC,SAAS,EAAE,CAAC,CAAC;YAEzF,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,wBAAwB,SAAS,MAAM;4BACvC,sBAAsB,IAAI,CAAC,aAAa,IAAI,KAAK,IAAI;4BACrD,wBAAwB,IAAI,CAAC,eAAe,IAAI,KAAK,MAAM;4BAC3D,mBAAmB,IAAI,CAAC,SAAS,IAAI,KAAK,IAAI;4BAC9C,iBAAiB,IAAI,CAAC,QAAQ,IAAI,KAAK,IAAI;4BAC3C,oBAAoB,IAAI,CAAC,WAAW,IAAI,KAAK,GAAG;qBACvD;iBACF;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kDAAkD;YAClD,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,uBAAuB;4BACvB,eAAe,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,kBAAkB,IAAI;4BACjF,gEAAgE;4BAChE,oBAAoB,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK;qBACzE;iBACF;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,IAAS;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAE,IAAY,EAAE,KAAK,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;QAExD,IAAI,CAAC;YACH,wEAAwE;YACxE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;YAEhF,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAC1C,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,cAAc,GAAG,CAAC,KAAK,eAAe,GAAG,CAAC,MAAM,iBAAiB,GAAG,CAAC,QAAQ,IAAI,CAC/H,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,mBAAmB,CAAC;YAEtC,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,uBAAuB,KAAK,gBAAgB,OAAO,EAAE;qBAC5D;iBACF;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,uBAAuB;4BACvB,eAAe,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,kBAAkB,IAAI;4BACjF,+DAA+D;4BAC/D,0EAA0E;qBACjF;iBACF;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;YAC9B,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC;QAEF,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;YAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,GAAG;QACP,MAAM,SAAS,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACrC,OAAO,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;IACtD,CAAC;CACF;AAED,mBAAmB;AACnB,MAAM,MAAM,GAAG,IAAI,eAAe,EAAE,CAAC;AACrC,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IAC3B,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;IAC3D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}