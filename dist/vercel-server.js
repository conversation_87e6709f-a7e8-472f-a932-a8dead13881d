#!/usr/bin/env node
import { config } from 'dotenv';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema, } from '@modelcontextprotocol/sdk/types.js';
// Load environment variables
config();
class VercelMCPServer {
    server;
    config;
    baseUrl = 'https://api.vercel.com';
    constructor() {
        // Configuratie uit environment variables
        this.config = {
            token: process.env.VERCEL_TOKEN || '************************',
            teamId: process.env.VERCEL_TEAM_ID || '************************',
            aiGatewayKey: process.env.AI_GATEWAY_API_KEY || 'fO77tkCq1FYOaegT3yrr3yYg',
        };
        this.server = new Server({
            name: 'vercel-mcp-server',
            version: '1.0.0',
        });
        this.setupToolHandlers();
        this.setupErrorHandling();
    }
    async makeVercelRequest(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const headers = {
            'Authorization': `Bearer ${this.config.token}`,
            'Content-Type': 'application/json',
            ...options.headers,
        };
        if (this.config.teamId) {
            const separator = endpoint.includes('?') ? '&' : '?';
            endpoint += `${separator}teamId=${this.config.teamId}`;
        }
        const response = await fetch(`${this.baseUrl}${endpoint}`, {
            ...options,
            headers,
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Vercel API error: ${response.status} - ${errorText}`);
        }
        return response.json();
    }
    setupToolHandlers() {
        // List available tools
        this.server.setRequestHandler(ListToolsRequestSchema, async () => {
            return {
                tools: [
                    {
                        name: 'list_projects',
                        description: 'Lijst alle Vercel projecten',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                limit: {
                                    type: 'number',
                                    description: 'Aantal projecten om op te halen (max 100)',
                                    default: 20,
                                },
                            },
                        },
                    },
                    {
                        name: 'get_project',
                        description: 'Haal details van een specifiek project op',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                projectId: {
                                    type: 'string',
                                    description: 'Project ID of naam',
                                },
                            },
                            required: ['projectId'],
                        },
                    },
                    {
                        name: 'list_deployments',
                        description: 'Lijst deployments voor een project',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                projectId: {
                                    type: 'string',
                                    description: 'Project ID (optioneel)',
                                },
                                limit: {
                                    type: 'number',
                                    description: 'Aantal deployments (max 100)',
                                    default: 10,
                                },
                            },
                        },
                    },
                    {
                        name: 'get_deployment',
                        description: 'Haal details van een specifieke deployment op',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                deploymentId: {
                                    type: 'string',
                                    description: 'Deployment ID',
                                },
                            },
                            required: ['deploymentId'],
                        },
                    },
                    {
                        name: 'list_domains',
                        description: 'Lijst alle domeinen',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                limit: {
                                    type: 'number',
                                    description: 'Aantal domeinen (max 100)',
                                    default: 20,
                                },
                            },
                        },
                    },
                    {
                        name: 'get_team_info',
                        description: 'Haal team informatie op',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                        },
                    },
                    {
                        name: 'ai_gateway_stats',
                        description: 'Haal AI Gateway statistieken op',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                timeframe: {
                                    type: 'string',
                                    description: 'Tijdsperiode (1h, 24h, 7d, 30d)',
                                    default: '24h',
                                },
                            },
                        },
                    },
                    {
                        name: 'ai_gateway_logs',
                        description: 'Haal AI Gateway logs op',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                limit: {
                                    type: 'number',
                                    description: 'Aantal logs (max 100)',
                                    default: 10,
                                },
                            },
                        },
                    },
                ],
            };
        });
        // Handle tool calls
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            try {
                switch (name) {
                    case 'list_projects':
                        return await this.listProjects(args);
                    case 'get_project':
                        return await this.getProject(args);
                    case 'list_deployments':
                        return await this.listDeployments(args);
                    case 'get_deployment':
                        return await this.getDeployment(args);
                    case 'list_domains':
                        return await this.listDomains(args);
                    case 'get_team_info':
                        return await this.getTeamInfo();
                    case 'ai_gateway_stats':
                        return await this.getAIGatewayStats(args);
                    case 'ai_gateway_logs':
                        return await this.getAIGatewayLogs(args);
                    default:
                        throw new Error(`Unknown tool: ${name}`);
                }
            }
            catch (error) {
                return {
                    content: [
                        {
                            type: 'text',
                            text: `Error: ${error instanceof Error ? error.message : String(error)}`,
                        },
                    ],
                    isError: true,
                };
            }
        });
    }
    async listProjects(args) {
        const limit = Math.min(args?.limit || 20, 100);
        const data = await this.makeVercelRequest(`/v9/projects?limit=${limit}`);
        const projectList = data.projects.map((project) => `• ${project.name} (${project.id})\n  Framework: ${project.framework || 'N/A'}\n  Created: ${new Date(project.createdAt).toLocaleDateString()}\n  URL: https://${project.name}.vercel.app`).join('\n\n');
        return {
            content: [
                {
                    type: 'text',
                    text: `📁 Vercel Projecten (${data.projects.length}):\n\n${projectList}`,
                },
            ],
        };
    }
    async getProject(args) {
        const projectId = args?.projectId;
        if (!projectId)
            throw new Error('Project ID is required');
        const data = await this.makeVercelRequest(`/v9/projects/${projectId}`);
        return {
            content: [
                {
                    type: 'text',
                    text: `📁 Project: ${data.name}\n` +
                        `🆔 ID: ${data.id}\n` +
                        `🔧 Framework: ${data.framework || 'N/A'}\n` +
                        `📅 Created: ${new Date(data.createdAt).toLocaleDateString()}\n` +
                        `📅 Updated: ${new Date(data.updatedAt).toLocaleDateString()}\n` +
                        `🌐 Production URL: https://${data.name}.vercel.app\n` +
                        `📂 Root Directory: ${data.rootDirectory || '/'}\n` +
                        `🔨 Build Command: ${data.buildCommand || 'Auto-detected'}\n` +
                        `📤 Output Directory: ${data.outputDirectory || 'Auto-detected'}`,
                },
            ],
        };
    }
    async listDeployments(args) {
        const projectId = args?.projectId;
        const limit = Math.min(args?.limit || 10, 100);
        let endpoint = `/v6/deployments?limit=${limit}`;
        if (projectId) {
            endpoint += `&projectId=${projectId}`;
        }
        const data = await this.makeVercelRequest(endpoint);
        const deploymentList = data.deployments.map((deployment) => `• ${deployment.url}\n  State: ${deployment.state}\n  Created: ${new Date(deployment.createdAt).toLocaleDateString()}\n  ID: ${deployment.uid}`).join('\n\n');
        return {
            content: [
                {
                    type: 'text',
                    text: `🚀 Deployments (${data.deployments.length}):\n\n${deploymentList}`,
                },
            ],
        };
    }
    async getDeployment(args) {
        const deploymentId = args?.deploymentId;
        if (!deploymentId)
            throw new Error('Deployment ID is required');
        const data = await this.makeVercelRequest(`/v13/deployments/${deploymentId}`);
        return {
            content: [
                {
                    type: 'text',
                    text: `🚀 Deployment: ${data.url}\n` +
                        `🆔 ID: ${data.uid}\n` +
                        `📊 State: ${data.state}\n` +
                        `📅 Created: ${new Date(data.createdAt).toLocaleDateString()}\n` +
                        `⏱️ Build Time: ${data.buildingAt ? `${Math.round((new Date(data.readyAt || Date.now()).getTime() - new Date(data.buildingAt).getTime()) / 1000)}s` : 'N/A'}\n` +
                        `🌐 URL: https://${data.url}\n` +
                        `📁 Project: ${data.name || 'N/A'}\n` +
                        `🔧 Source: ${data.meta?.githubCommitMessage || 'N/A'}`,
                },
            ],
        };
    }
    async listDomains(args) {
        const limit = Math.min(args?.limit || 20, 100);
        const data = await this.makeVercelRequest(`/v5/domains?limit=${limit}`);
        const domainList = data.domains.map((domain) => `• ${domain.name}\n  Verified: ${domain.verified ? '✅' : '❌'}\n  Created: ${new Date(domain.createdAt).toLocaleDateString()}`).join('\n\n');
        return {
            content: [
                {
                    type: 'text',
                    text: `🌐 Domeinen (${data.domains.length}):\n\n${domainList}`,
                },
            ],
        };
    }
    async getTeamInfo() {
        const data = await this.makeVercelRequest('/v2/user');
        return {
            content: [
                {
                    type: 'text',
                    text: `👤 Account Info:\n` +
                        `📧 Email: ${data.email}\n` +
                        `👤 Username: ${data.username}\n` +
                        `🆔 ID: ${data.id}\n` +
                        `📅 Created: ${new Date(data.createdAt).toLocaleDateString()}`,
                },
            ],
        };
    }
    async getAIGatewayStats(args) {
        const timeframe = args?.timeframe || '24h';
        try {
            // AI Gateway stats endpoint (hypothetisch - pas aan naar werkelijke API)
            const data = await this.makeVercelRequest(`/v1/ai-gateway/stats?timeframe=${timeframe}`);
            return {
                content: [
                    {
                        type: 'text',
                        text: `🤖 AI Gateway Stats (${timeframe}):\n` +
                            `📊 Total Requests: ${data.totalRequests || 'N/A'}\n` +
                            `⚡ Avg Response Time: ${data.avgResponseTime || 'N/A'}ms\n` +
                            `💰 Total Cost: $${data.totalCost || 'N/A'}\n` +
                            `🔥 Top Model: ${data.topModel || 'N/A'}\n` +
                            `📈 Success Rate: ${data.successRate || 'N/A'}%`,
                    },
                ],
            };
        }
        catch (error) {
            // Fallback als AI Gateway API niet beschikbaar is
            return {
                content: [
                    {
                        type: 'text',
                        text: `🤖 AI Gateway Info:\n` +
                            `🔑 API Key: ${this.config.aiGatewayKey ? '✅ Configured' : '❌ Not configured'}\n` +
                            `📝 Note: AI Gateway stats endpoint may not be available yet.\n` +
                            `🔧 Your API Key: ${this.config.aiGatewayKey?.substring(0, 8)}...`,
                    },
                ],
            };
        }
    }
    async getAIGatewayLogs(args) {
        const limit = Math.min(args?.limit || 10, 100);
        try {
            // AI Gateway logs endpoint (hypothetisch - pas aan naar werkelijke API)
            const data = await this.makeVercelRequest(`/v1/ai-gateway/logs?limit=${limit}`);
            const logList = data.logs?.map((log) => `• ${new Date(log.timestamp).toLocaleString()}\n  Model: ${log.model}\n  Status: ${log.status}\n  Duration: ${log.duration}ms`).join('\n\n') || 'No logs available';
            return {
                content: [
                    {
                        type: 'text',
                        text: `📋 AI Gateway Logs (${limit} recent):\n\n${logList}`,
                    },
                ],
            };
        }
        catch (error) {
            return {
                content: [
                    {
                        type: 'text',
                        text: `📋 AI Gateway Logs:\n` +
                            `🔑 API Key: ${this.config.aiGatewayKey ? '✅ Configured' : '❌ Not configured'}\n` +
                            `📝 Note: AI Gateway logs endpoint may not be available yet.\n` +
                            `💡 You can use this key to make direct API calls to AI Gateway services.`,
                    },
                ],
            };
        }
    }
    setupErrorHandling() {
        this.server.onerror = (error) => {
            console.error('[Vercel MCP Error]', error);
        };
        process.on('SIGINT', async () => {
            await this.server.close();
            process.exit(0);
        });
    }
    async run() {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        console.error('Vercel MCP Server running on stdio');
    }
}
// Start the server
const server = new VercelMCPServer();
server.run().catch((error) => {
    console.error('Failed to start Vercel MCP server:', error);
    process.exit(1);
});
//# sourceMappingURL=vercel-server.js.map