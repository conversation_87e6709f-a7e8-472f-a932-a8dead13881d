# Vercel MCP Server Setup Guide

## 🎯 Wat hebben we gebouwd?

Een complete MCP (Model Context Protocol) server die integreert met:
- **Vercel API** - Voor project en deployment management
- **AI Gateway** - Voor AI service monitoring en analytics

## 🔑 Je API Credentials

```env
VERCEL_TOKEN=************************
VERCEL_TEAM_ID=************************
AI_GATEWAY_API_KEY=fO77tkCq1FYOaegT3yrr3yYg
```

## 🚀 Beschikbare Tools

### Vercel Project Management
- `list_projects` - Bekijk al je Vercel projecten
- `get_project` - Gedetailleerde project informatie
- `list_deployments` - Deployment geschiedenis
- `get_deployment` - Specifieke deployment details
- `list_domains` - Alle gekoppelde domeinen
- `get_team_info` - Account/team informatie

### AI Gateway Monitoring
- `ai_gateway_stats` - G<PERSON><PERSON><PERSON> statistieken
- `ai_gateway_logs` - Recent logs en activiteit

## 📋 Snelle Start

1. **Server starten:**
   ```bash
   npm run start:vercel
   ```

2. **Claude Desktop configureren:**
   - Kopieer `claude-desktop-config.json` naar je Claude Desktop config
   - Herstart Claude Desktop

3. **Testen in Claude:**
   ```
   Kun je mijn Vercel projecten laten zien?
   Wat zijn mijn recente deployments?
   Hoe presteren mijn AI Gateway services?
   ```

## 🔧 Troubleshooting

### 403 Forbidden Errors
Als je 403 errors krijgt:

1. **Controleer je Vercel token:**
   - Ga naar https://vercel.com/account/tokens
   - Maak een nieuwe token aan met de juiste permissions
   - Update je `.env` file

2. **Team ID verificatie:**
   - Ga naar je Vercel dashboard
   - Check de URL voor je team ID
   - Zorg dat je token toegang heeft tot het team

3. **API Permissions:**
   - Zorg dat je token de volgende permissions heeft:
     - Read projects
     - Read deployments
     - Read domains
     - Read team info

### AI Gateway Setup
- AI Gateway is mogelijk nog in beta
- Sommige endpoints zijn mogelijk nog niet beschikbaar
- De server toont je API key status als fallback

## 🎉 Volgende Stappen

1. **Test alle tools** in Claude Desktop
2. **Voeg meer Vercel endpoints toe** naar behoefte
3. **Integreer met andere services** (GitHub, etc.)
4. **Maak custom workflows** voor deployment automation

## 💡 Tips

- Gebruik `list_projects` om project IDs te vinden
- Deployment IDs kun je krijgen via `list_deployments`
- De server cached geen data - alle info is real-time
- Environment variables worden automatisch geladen uit `.env`

Veel plezier met je nieuwe Vercel MCP server! 🎊
