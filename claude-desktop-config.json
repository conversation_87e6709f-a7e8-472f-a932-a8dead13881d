{"mcpServers": {"basic-mcp-server": {"command": "node", "args": ["/Users/<USER>/.continue/mcp/dist/index.js"]}, "vercel-mcp-server": {"command": "node", "args": ["/Users/<USER>/.continue/mcp/dist/vercel-server.js"], "env": {"VERCEL_TOKEN": "************************", "VERCEL_TEAM_ID": "************************", "AI_GATEWAY_API_KEY": "fO77tkCq1FYOaegT3yrr3yYg"}}, "lmstudio-mcp-server": {"command": "node", "args": ["/Users/<USER>/.continue/mcp/dist/lmstudio-server.js"], "env": {"LM_STUDIO_BASE_URL": "http://localhost:1234", "LM_STUDIO_API_KEY": ""}}}}