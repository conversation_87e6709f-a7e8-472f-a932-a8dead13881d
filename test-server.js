#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Test de MCP server door JSON-RPC berichten te sturen
function testMCPServer() {
  console.log('🚀 Starting MCP Server test...\n');
  
  const serverPath = join(__dirname, 'dist', 'index.js');
  const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let responseCount = 0;
  const expectedResponses = 3;

  server.stdout.on('data', (data) => {
    const response = data.toString();
    console.log('📥 Server response:', response);
    responseCount++;
    
    if (responseCount >= expectedResponses) {
      console.log('\n✅ All tests completed successfully!');
      server.kill();
      process.exit(0);
    }
  });

  server.stderr.on('data', (data) => {
    console.log('📝 Server log:', data.toString());
  });

  server.on('close', (code) => {
    console.log(`\n🔚 Server process exited with code ${code}`);
  });

  // Test 1: List tools
  setTimeout(() => {
    console.log('📤 Test 1: Listing available tools...');
    const listToolsRequest = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/list',
      params: {}
    };
    server.stdin.write(JSON.stringify(listToolsRequest) + '\n');
  }, 100);

  // Test 2: Echo tool
  setTimeout(() => {
    console.log('📤 Test 2: Testing echo tool...');
    const echoRequest = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'echo',
        arguments: {
          text: 'Hello from MCP test!'
        }
      }
    };
    server.stdin.write(JSON.stringify(echoRequest) + '\n');
  }, 200);

  // Test 3: Calculate tool
  setTimeout(() => {
    console.log('📤 Test 3: Testing calculate tool...');
    const calculateRequest = {
      jsonrpc: '2.0',
      id: 3,
      method: 'tools/call',
      params: {
        name: 'calculate',
        arguments: {
          expression: '2 + 3 * 4'
        }
      }
    };
    server.stdin.write(JSON.stringify(calculateRequest) + '\n');
  }, 300);

  // Timeout fallback
  setTimeout(() => {
    console.log('\n⏰ Test timeout reached');
    server.kill();
    process.exit(1);
  }, 5000);
}

testMCPServer();
