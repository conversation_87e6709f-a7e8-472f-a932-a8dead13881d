# LM Studio MCP Server Setup Guide

## 🎯 Wat hebben we gebouwd?

Een complete MCP (Model Context Protocol) server die integreert met LM Studio's lokale API. Je kunt nu vanuit Claude Desktop direct communiceren met je lokale LM Studio modellen!

## 🤖 Je LM Studio Setup

**Gedetecteerde modellen (17):**
- deepseek-coder-v2-lite-instruct
- openai/gpt-oss-20b
- text-embedding-nomic-embed-text-v1.5
- deepseek-coder-33b-instruct
- qwen3-coder-30b-a3b-instruct-mlx-mixed_3_6
- kimi-dev-72b
- qwen/qwen2.5-coder-32b
- hermes-3-llama-3.1-8b
- qwen3-8b
- nous-hermes-2-yi-34b
- starcoder2-15b-instruct-v0.1
- qwen/qwen2.5-coder-14b
- microsoft/phi-4-mini-reasoning
- deepseek-coder-6.7b-kexer
- llava-v1.5-7b-llamafile
- codellama-7b-kstack
- wizardlm-2-7b

## 🚀 Beschikbare Tools

### LM Studio Communicatie
- `list_models` - <PERSON><PERSON><PERSON> alle geladen modellen
- `chat_completion` - Stuur berichten naar LM Studio modellen
- `get_server_info` - Server status en informatie
- `check_connection` - Test verbinding met LM Studio
- `embeddings` - Genereer text embeddings

## 📋 Snelle Start

### 1. Zorg dat LM Studio draait
```bash
# LM Studio moet draaien op http://localhost:1234
# Start LM Studio en laad een model
```

### 2. Test de MCP server
```bash
npm run start:lmstudio
```

### 3. Claude Desktop configureren
De configuratie is al gekopieerd naar Claude Desktop. Herstart Claude Desktop.

### 4. Test in Claude Desktop
```
Welke LM Studio modellen heb ik beschikbaar?
```
```
Stuur een bericht naar LM Studio: "Hallo, kun je me helpen met Python code?"
```
```
Controleer de verbinding met LM Studio
```

## 🎮 Praktische Voorbeelden

### Chat met je lokale modellen
```
Stuur naar LM Studio: "Schrijf een Python functie om een lijst te sorteren"
```

### Model vergelijking
```
Lijst mijn LM Studio modellen en vertel me welke het beste is voor code generatie
```

### Embeddings genereren
```
Genereer embeddings voor: "Machine learning is fascinating"
```

## 🔧 LM Studio Setup Tips

### 1. Server starten in LM Studio
- Open LM Studio
- Ga naar "Local Server" tab
- Klik "Start Server"
- Zorg dat het draait op poort 1234

### 2. Model laden
- Ga naar "Chat" tab
- Selecteer een model uit je collectie
- Wacht tot het model geladen is

### 3. API toegang
- LM Studio gebruikt OpenAI-compatible API
- Standaard geen API key nodig voor lokale toegang
- Base URL: http://localhost:1234

## 🎯 Geavanceerde Features

### Temperature Control
```
Stuur naar LM Studio met temperature 0.1: "Geef me een exacte code implementatie"
```

### Token Limits
```
Stuur een kort bericht naar LM Studio (max 100 tokens): "Wat is AI?"
```

### Model Selection
```
Gebruik het deepseek-coder model om: "Een REST API in Python te maken"
```

## 🔍 Troubleshooting

### LM Studio niet bereikbaar
1. **Controleer of LM Studio draait**
   ```bash
   curl http://localhost:1234/v1/models
   ```

2. **Controleer poort**
   - Standaard: 1234
   - Wijzig in `.env` als anders: `LM_STUDIO_BASE_URL=http://localhost:JOUW_POORT`

3. **Model laden**
   - Zorg dat een model geladen is in LM Studio
   - Sommige tools werken alleen met geladen modellen

### Performance Tips
- **Kleinere modellen** voor snellere responses
- **Grotere modellen** voor betere kwaliteit
- **Embedding modellen** voor text similarity taken

## 🎊 Je bent klaar!

Je hebt nu:
✅ LM Studio MCP server werkend
✅ 17 modellen beschikbaar
✅ Claude Desktop configuratie
✅ Directe communicatie tussen Claude en LM Studio

**Test het nu in Claude Desktop!** 

Probeer: "Laat mijn LM Studio modellen zien en stuur een test bericht naar het beste model voor code generatie."
