#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Test de LM Studio MCP server
function testLMStudioMCPServer() {
  console.log('🚀 Starting LM Studio MCP Server test...\n');
  
  const serverPath = join(__dirname, 'dist', 'lmstudio-server.js');
  const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let responseCount = 0;
  const expectedResponses = 4;

  server.stdout.on('data', (data) => {
    const response = data.toString();
    console.log('📥 Server response:', response);
    responseCount++;
    
    if (responseCount >= expectedResponses) {
      console.log('\n✅ All LM Studio tests completed!');
      server.kill();
      process.exit(0);
    }
  });

  server.stderr.on('data', (data) => {
    console.log('📝 Server log:', data.toString());
  });

  server.on('close', (code) => {
    console.log(`\n🔚 LM Studio server process exited with code ${code}`);
  });

  // Test 1: List tools
  setTimeout(() => {
    console.log('📤 Test 1: Listing LM Studio tools...');
    const listToolsRequest = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/list',
      params: {}
    };
    server.stdin.write(JSON.stringify(listToolsRequest) + '\n');
  }, 100);

  // Test 2: Check connection
  setTimeout(() => {
    console.log('📤 Test 2: Checking LM Studio connection...');
    const connectionRequest = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'check_connection',
        arguments: {}
      }
    };
    server.stdin.write(JSON.stringify(connectionRequest) + '\n');
  }, 200);

  // Test 3: List models
  setTimeout(() => {
    console.log('📤 Test 3: Listing available models...');
    const modelsRequest = {
      jsonrpc: '2.0',
      id: 3,
      method: 'tools/call',
      params: {
        name: 'list_models',
        arguments: {}
      }
    };
    server.stdin.write(JSON.stringify(modelsRequest) + '\n');
  }, 300);

  // Test 4: Get server info
  setTimeout(() => {
    console.log('📤 Test 4: Getting server info...');
    const serverInfoRequest = {
      jsonrpc: '2.0',
      id: 4,
      method: 'tools/call',
      params: {
        name: 'get_server_info',
        arguments: {}
      }
    };
    server.stdin.write(JSON.stringify(serverInfoRequest) + '\n');
  }, 400);

  // Timeout fallback
  setTimeout(() => {
    console.log('\n⏰ Test timeout reached');
    server.kill();
    process.exit(1);
  }, 10000);
}

testLMStudioMCPServer();
